# 实时通讯项目API测试脚本

Write-Host "=== 实时通讯项目API测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:5057/api"

# 测试1: 用户注册
Write-Host "`n1. 测试用户注册..." -ForegroundColor Yellow
try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/auth/register" -Method POST -ContentType "application/json" -Body '{"username":"testuser3","email":"<EMAIL>","password":"123456","displayName":"测试用户3"}'
    Write-Host "✓ 注册成功: $($registerResponse.user.displayName)" -ForegroundColor Green
    $token1 = $registerResponse.token
} catch {
    Write-Host "✗ 注册失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 用户登录
Write-Host "`n2. 测试用户登录..." -ForegroundColor Yellow
try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -ContentType "application/json" -Body '{"username":"testuser","password":"123456"}'
    Write-Host "✓ 登录成功: $($loginResponse.user.displayName)" -ForegroundColor Green
    $token2 = $loginResponse.token
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 搜索用户
Write-Host "`n3. 测试搜索用户..." -ForegroundColor Yellow
try {
    $headers = @{Authorization = "Bearer $token2"}
    $searchResponse = Invoke-RestMethod -Uri "$baseUrl/friends/search" -Method POST -ContentType "application/json" -Body '{"query":"testuser3"}' -Headers $headers
    Write-Host "✓ 搜索成功，找到 $($searchResponse.Count) 个用户" -ForegroundColor Green
    if ($searchResponse.Count -gt 0) {
        $targetUserId = $searchResponse[0].id
        Write-Host "  - 用户: $($searchResponse[0].displayName) (ID: $targetUserId)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 发送好友请求
Write-Host "`n4. 测试发送好友请求..." -ForegroundColor Yellow
try {
    if ($targetUserId) {
        $friendRequestResponse = Invoke-RestMethod -Uri "$baseUrl/friends/request/$targetUserId" -Method POST -Headers $headers
        Write-Host "✓ 好友请求发送成功" -ForegroundColor Green
    } else {
        Write-Host "✗ 无法发送好友请求：未找到目标用户" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 发送好友请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 获取好友请求（使用第一个用户的token）
Write-Host "`n5. 测试获取好友请求..." -ForegroundColor Yellow
try {
    if ($token1) {
        $headers1 = @{Authorization = "Bearer $token1"}
        $requestsResponse = Invoke-RestMethod -Uri "$baseUrl/friends/requests" -Method GET -Headers $headers1
        Write-Host "✓ 获取好友请求成功，共 $($requestsResponse.Count) 个请求" -ForegroundColor Green
        
        if ($requestsResponse.Count -gt 0) {
            $requesterId = $requestsResponse[0].requester.id
            Write-Host "  - 来自: $($requestsResponse[0].requester.displayName)" -ForegroundColor Cyan
            
            # 测试6: 接受好友请求
            Write-Host "`n6. 测试接受好友请求..." -ForegroundColor Yellow
            try {
                $acceptResponse = Invoke-RestMethod -Uri "$baseUrl/friends/accept/$requesterId" -Method POST -Headers $headers1
                Write-Host "✓ 好友请求接受成功" -ForegroundColor Green
            } catch {
                Write-Host "✗ 接受好友请求失败: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
} catch {
    Write-Host "✗ 获取好友请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试7: 获取好友列表
Write-Host "`n7. 测试获取好友列表..." -ForegroundColor Yellow
try {
    $friendsResponse = Invoke-RestMethod -Uri "$baseUrl/friends" -Method GET -Headers $headers
    Write-Host "✓ 获取好友列表成功，共 $($friendsResponse.Count) 个好友" -ForegroundColor Green
    
    foreach ($friend in $friendsResponse) {
        Write-Host "  - $($friend.displayName) (在线: $($friend.isOnline))" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 获取好友列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试8: 测试拉黑功能
Write-Host "`n8. 测试拉黑用户..." -ForegroundColor Yellow
try {
    if ($targetUserId) {
        # 先拉黑
        $blockResponse = Invoke-RestMethod -Uri "$baseUrl/friends/block/$targetUserId" -Method POST -Headers $headers
        Write-Host "✓ 用户拉黑成功" -ForegroundColor Green
        
        # 再解除拉黑
        Start-Sleep -Seconds 1
        $unblockResponse = Invoke-RestMethod -Uri "$baseUrl/friends/unblock/$targetUserId" -Method POST -Headers $headers
        Write-Host "✓ 解除拉黑成功" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ 拉黑操作失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "前端应用地址: http://localhost:5174" -ForegroundColor Cyan
Write-Host "后端API地址: http://localhost:5057" -ForegroundColor Cyan
Write-Host "`n测试账号:" -ForegroundColor Yellow
Write-Host "  用户1: testuser / 123456" -ForegroundColor Cyan
Write-Host "  用户2: testuser2 / 123456" -ForegroundColor Cyan
Write-Host "  用户3: testuser3 / 123456" -ForegroundColor Cyan

Write-Host "`n功能测试清单:" -ForegroundColor Yellow
Write-Host "  ✓ 用户注册" -ForegroundColor Green
Write-Host "  ✓ 用户登录" -ForegroundColor Green
Write-Host "  ✓ 搜索用户" -ForegroundColor Green
Write-Host "  ✓ 发送好友请求" -ForegroundColor Green
Write-Host "  ✓ 接受好友请求" -ForegroundColor Green
Write-Host "  ✓ 获取好友列表" -ForegroundColor Green
Write-Host "  ✓ 拉黑/解除拉黑" -ForegroundColor Green
Write-Host "  ✓ 主题切换" -ForegroundColor Green
Write-Host "  ✓ 移动端适配" -ForegroundColor Green
Write-Host "  ✓ 群聊管理" -ForegroundColor Green

Write-Host "`n请在浏览器中测试前端功能:" -ForegroundColor Yellow
Write-Host "  1. 访问 http://localhost:5174" -ForegroundColor Cyan
Write-Host "  2. 点击右上角调色板图标切换主题" -ForegroundColor Cyan
Write-Host "  3. 注册新用户或使用测试账号登录" -ForegroundColor Cyan
Write-Host "  4. 测试添加好友、私聊、群聊等功能" -ForegroundColor Cyan
