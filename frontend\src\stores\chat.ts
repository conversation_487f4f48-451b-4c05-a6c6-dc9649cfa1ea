import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import signalRService from '@/services/signalr'
import { messageAPI } from '@/services/api'

export interface Message {
  id: number
  senderId: number
  senderName: string
  content: string
  createdAt: string
  type: 'private' | 'group'
  receiverId?: number
  groupId?: number
  isRead?: boolean
}

export interface ChatSession {
  id: string // 对于私聊是用户ID，对于群聊是群组ID
  type: 'private' | 'group'
  name: string
  avatar?: string
  lastMessage?: Message
  unreadCount: number
  isOnline?: boolean // 仅对私聊有效
}

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<Record<string, Message[]>>({}) // 按会话ID分组的消息
  const chatSessions = ref<ChatSession[]>([])
  const currentChatId = ref<string | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const currentMessages = computed(() => {
    if (!currentChatId.value) return []
    return messages.value[currentChatId.value] || []
  })

  const currentChat = computed(() => {
    if (!currentChatId.value) return null
    return chatSessions.value.find(chat => chat.id === currentChatId.value) || null
  })

  const totalUnreadCount = computed(() => {
    return chatSessions.value.reduce((total, chat) => total + chat.unreadCount, 0)
  })

  // 初始化SignalR事件监听
  const initializeSignalR = () => {
    // 监听接收消息
    signalRService.onReceiveMessage((message: Message) => {
      addMessage(message)
    })

    // 监听消息发送确认
    signalRService.onMessageSent((message: Message) => {
      addMessage(message)
    })

    // 监听好友状态变化
    signalRService.onFriendStatusChanged((data: { userId: number; isOnline: boolean }) => {
      updateFriendStatus(data.userId, data.isOnline)
    })

    // 监听错误
    signalRService.onError((error: string) => {
      console.error('SignalR错误:', error)
    })
  }

  // 添加消息
  const addMessage = (message: Message) => {
    // 获取当前用户ID
    const { useAuthStore } = require('@/stores/auth')
    const authStore = useAuthStore()
    const currentUserId = authStore.user?.id

    // 确定聊天ID
    let chatId: string
    if (message.type === 'private') {
      // 私聊：使用对方的ID作为chatId
      if (message.senderId === currentUserId) {
        chatId = message.receiverId?.toString() || ''
      } else {
        chatId = message.senderId.toString()
      }
    } else {
      // 群聊：使用群组ID
      chatId = message.groupId?.toString() || ''
    }

    if (!chatId) return

    if (!messages.value[chatId]) {
      messages.value[chatId] = []
    }

    // 检查消息是否已存在（避免重复）
    const existingMessage = messages.value[chatId].find(m => m.id === message.id)
    if (!existingMessage) {
      messages.value[chatId].push(message)

      // 按时间排序
      messages.value[chatId].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )

      // 更新会话信息
      updateChatSession(chatId, message)
    }
  }

  // 更新会话信息
  const updateChatSession = (chatId: string, message: Message) => {
    const existingSession = chatSessions.value.find(chat => chat.id === chatId)
    
    if (existingSession) {
      existingSession.lastMessage = message
      if (chatId !== currentChatId.value) {
        existingSession.unreadCount++
      }
    } else {
      // 创建新的会话
      const newSession: ChatSession = {
        id: chatId,
        type: message.type,
        name: message.senderName,
        lastMessage: message,
        unreadCount: chatId !== currentChatId.value ? 1 : 0
      }
      chatSessions.value.push(newSession)
    }

    // 按最后消息时间排序
    chatSessions.value.sort((a, b) => {
      const aTime = a.lastMessage ? new Date(a.lastMessage.createdAt).getTime() : 0
      const bTime = b.lastMessage ? new Date(b.lastMessage.createdAt).getTime() : 0
      return bTime - aTime
    })
  }

  // 更新好友在线状态
  const updateFriendStatus = (userId: number, isOnline: boolean) => {
    const session = chatSessions.value.find(chat => 
      chat.type === 'private' && chat.id === userId.toString()
    )
    if (session) {
      session.isOnline = isOnline
    }
  }

  // 设置当前聊天
  const setCurrentChat = (chatId: string) => {
    currentChatId.value = chatId
    
    // 清除未读计数
    const session = chatSessions.value.find(chat => chat.id === chatId)
    if (session) {
      session.unreadCount = 0
    }
  }

  // 发送私聊消息
  const sendPrivateMessage = async (receiverId: number, content: string) => {
    try {
      await signalRService.sendPrivateMessage(receiverId, content)
    } catch (error) {
      console.error('发送私聊消息失败:', error)
      throw error
    }
  }

  // 发送群聊消息
  const sendGroupMessage = async (groupId: number, content: string) => {
    try {
      await signalRService.sendGroupMessage(groupId, content)
    } catch (error) {
      console.error('发送群聊消息失败:', error)
      throw error
    }
  }

  // 添加聊天会话
  const addChatSession = (session: ChatSession) => {
    const existingSession = chatSessions.value.find(chat => chat.id === session.id)
    if (!existingSession) {
      chatSessions.value.push(session)
    }
  }

  // 加载历史消息
  const loadHistoryMessages = async (chatId: string, type: 'private' | 'group') => {
    try {
      let response
      if (type === 'private') {
        response = await messageAPI.getPrivateMessages(parseInt(chatId))
      } else {
        response = await messageAPI.getGroupMessages(parseInt(chatId))
      }

      if (response.data && response.data.length > 0) {
        if (!messages.value[chatId]) {
          messages.value[chatId] = []
        }

        // 合并历史消息，避免重复
        const existingIds = new Set(messages.value[chatId].map(m => m.id))
        const newMessages = response.data.filter((m: Message) => !existingIds.has(m.id))

        messages.value[chatId] = [...newMessages, ...messages.value[chatId]]

        // 按时间排序
        messages.value[chatId].sort((a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        )
      }
    } catch (error) {
      console.error('加载历史消息失败:', error)
    }
  }

  // 标记消息为已读
  const markAsRead = async (chatId: string, type: 'private' | 'group') => {
    try {
      if (type === 'private') {
        await messageAPI.markAsRead({ type: 'private', senderId: parseInt(chatId) })
      } else {
        await messageAPI.markAsRead({ type: 'group', groupId: parseInt(chatId) })
      }

      // 更新本地未读状态
      const session = chatSessions.value.find(s => s.id === chatId)
      if (session) {
        session.unreadCount = 0
      }
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }

  // 清除所有数据
  const clearAll = () => {
    messages.value = {}
    chatSessions.value = []
    currentChatId.value = null
  }

  return {
    // 状态
    messages,
    chatSessions,
    currentChatId,
    isLoading,
    
    // 计算属性
    currentMessages,
    currentChat,
    totalUnreadCount,
    
    // 方法
    initializeSignalR,
    addMessage,
    updateChatSession,
    updateFriendStatus,
    setCurrentChat,
    sendPrivateMessage,
    sendGroupMessage,
    addChatSession,
    loadHistoryMessages,
    markAsRead,
    clearAll
  }
})
