/* 移动端适配样式 */

/* 基础设置 */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 防止iOS Safari底部安全区域问题 */
body {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 移动端触摸优化 */
.van-button,
.van-cell,
.van-tabbar-item {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 响应式断点 */
@media (max-width: 480px) {
  /* 小屏手机 */
  .container {
    padding: 12px;
  }
  
  .van-nav-bar__title {
    font-size: 16px;
  }
  
  .van-cell__title {
    font-size: 14px;
  }
  
  .van-button--small {
    padding: 0 8px;
    font-size: 12px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  /* 大屏手机和小平板 */
  .container {
    padding: 16px;
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (min-width: 769px) {
  /* 平板和桌面 */
  .container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }
  
  /* 在大屏幕上限制最大宽度 */
  .chat-container,
  .home-container,
  .contacts-container,
  .profile-container {
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .van-nav-bar {
    height: 40px;
  }
  
  .van-nav-bar__content {
    height: 40px;
  }
  
  .van-tabbar {
    height: 40px;
  }
  
  .van-tabbar-item {
    padding: 4px 0;
  }
  
  .van-tabbar-item__text {
    font-size: 10px;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .van-image img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --van-background-color: #1a1a1a;
    --van-background-color-light: #2a2a2a;
    --van-text-color: #ffffff;
    --van-text-color-2: #cccccc;
    --van-text-color-3: #999999;
    --van-border-color: #333333;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .van-button:hover {
    background-color: var(--van-button-default-background-color);
  }
  
  .van-cell:hover {
    background-color: var(--van-cell-background-color);
  }
}

/* iOS Safari特殊处理 */
@supports (-webkit-touch-callout: none) {
  .input-area {
    padding-bottom: calc(12px + env(safe-area-inset-bottom));
  }
  
  .van-tabbar {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* 键盘弹出时的处理 */
.keyboard-open {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* 聊天界面移动端优化 */
.message-list {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.message-bubble {
  max-width: calc(100vw - 120px);
  word-break: break-word;
}

/* 输入框移动端优化 */
.message-input input {
  font-size: 16px; /* 防止iOS缩放 */
  -webkit-appearance: none;
  border-radius: 0;
}

/* 头像和图片优化 */
.van-image {
  background-color: #f7f8fa;
}

.van-image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/* 加载状态优化 */
.van-loading {
  color: var(--van-primary-color);
}

/* 空状态优化 */
.van-empty {
  padding: 40px 20px;
}

@media (max-width: 480px) {
  .van-empty {
    padding: 30px 16px;
  }
  
  .van-empty__description {
    font-size: 14px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 性能优化 */
.message-item,
.chat-item,
.van-cell {
  will-change: transform;
  transform: translateZ(0);
}

/* 防止文本选择 */
.van-nav-bar,
.van-tabbar,
.van-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 输入框可以选择文本 */
.van-field__control,
.message-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
