<template>
  <div class="login-container">
    <!-- 主题切换按钮 -->
    <div class="theme-toggle">
      <van-icon name="palette-o" @click="toggleTheme" />
    </div>

    <!-- 顶部Logo区域 -->
    <div class="login-header">
      <div class="logo-container">
        <div class="app-logo">
          <van-icon name="chat-o" size="60" />
        </div>
        <h1 class="app-title">微聊</h1>
        <p class="app-subtitle">连接你我，沟通无界</p>
      </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <van-form @submit="handleLogin">
        <div class="form-container">
          <div class="input-group">
            <van-field
              v-model="form.username"
              name="username"
              placeholder="手机号/微信号/邮箱"
              :rules="[{ required: true, message: '请输入用户名或邮箱' }]"
              :border="false"
              class="login-input"
            />
          </div>

          <div class="input-group">
            <van-field
              v-model="form.password"
              type="password"
              name="password"
              placeholder="请输入密码"
              :rules="[{ required: true, message: '请输入密码' }]"
              :border="false"
              class="login-input"
            />
          </div>
        </div>

        <div class="login-actions">
          <van-button
            block
            type="primary"
            native-type="submit"
            :loading="isLoading"
            loading-text="登录中..."
            class="login-btn"
          >
            登录
          </van-button>
        </div>
      </van-form>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <span class="action-link" @click="showForgotPassword">找回密码</span>
        <span class="action-divider">|</span>
        <span class="action-link" @click="showEmergencyLogin">紧急冻结</span>
      </div>

      <!-- 注册链接 -->
      <div class="register-section">
        <van-button
          type="primary"
          plain
          block
          @click="goToRegister"
          class="register-btn"
        >
          注册新账号
        </van-button>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="login-footer">
      <p class="footer-text">登录即表示同意</p>
      <div class="footer-links">
        <span class="link-text" @click="showTerms">《服务协议》</span>
        <span>和</span>
        <span class="link-text" @click="showPrivacy">《隐私政策》</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'

const router = useRouter()
const authStore = useAuthStore()
const themeStore = useThemeStore()

const isLoading = ref(false)
const form = reactive({
  username: '',
  password: ''
})

const handleLogin = async () => {
  if (!form.username || !form.password) {
    showToast('请填写完整信息')
    return
  }

  isLoading.value = true
  try {
    await authStore.login(form.username, form.password)
    showToast.success('登录成功')

    // 等待状态更新后再跳转
    await nextTick()
    await router.push('/')
  } catch (error: any) {
    console.error('登录失败:', error)
    showToast.fail(error.response?.data?.message || '登录失败，请检查用户名和密码')
  } finally {
    isLoading.value = false
  }
}

const goToRegister = () => {
  router.push('/register')
}

const toggleTheme = () => {
  themeStore.toggleTheme()
  showToast(`已切换到${themeStore.theme.displayName}`)
}

const showForgotPassword = () => {
  showDialog({
    title: '找回密码',
    message: '此功能暂未开放，请联系管理员重置密码',
    confirmButtonText: '确定'
  })
}

const showEmergencyLogin = () => {
  showDialog({
    title: '紧急冻结',
    message: '如果您的账号被盗用，可以紧急冻结账号以保护安全',
    confirmButtonText: '确定'
  })
}

const showTerms = () => {
  showDialog({
    title: '服务协议',
    message: '感谢您使用我们的服务。请仔细阅读并同意我们的服务条款。',
    confirmButtonText: '确定'
  })
}

const showPrivacy = () => {
  showDialog({
    title: '隐私政策',
    message: '我们重视您的隐私，请查看我们的隐私政策了解数据处理方式。',
    confirmButtonText: '确定'
  })
}

onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: var(--theme-background);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.theme-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px 40px;
}

.logo-container {
  text-align: center;
}

.app-logo {
  margin-bottom: 20px;
  color: var(--theme-primary);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.app-title {
  font-size: 36px;
  font-weight: 300;
  margin-bottom: 8px;
  color: var(--theme-text);
  letter-spacing: 2px;
}

.app-subtitle {
  font-size: 14px;
  color: var(--theme-text-secondary);
  margin: 0;
  opacity: 0.8;
}

.login-form {
  background: var(--theme-surface);
  border-radius: 20px 20px 0 0;
  padding: 32px 24px 24px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.login-form::before {
  content: '';
  position: absolute;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 36px;
  height: 4px;
  background: var(--theme-border);
  border-radius: 2px;
}

.form-container {
  margin-bottom: 32px;
}

.input-group {
  margin-bottom: 16px;
  background: var(--theme-background);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--theme-border);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
}

.login-input {
  background: transparent;
  padding: 16px;
  font-size: 16px;
}

.login-input :deep(.van-field__control) {
  background: transparent;
  color: var(--theme-text);
  font-size: 16px;
}

.login-input :deep(.van-field__control::placeholder) {
  color: var(--theme-text-secondary);
}

.login-btn {
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  background: var(--theme-primary);
  border: none;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
}

.quick-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 24px 0;
  gap: 12px;
}

.action-link {
  color: var(--theme-primary);
  font-size: 14px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.action-link:hover {
  opacity: 0.7;
}

.action-divider {
  color: var(--theme-border);
  font-size: 12px;
}

.register-section {
  margin-top: 20px;
}

.register-btn {
  height: 44px;
  border-radius: 22px;
  border: 1px solid var(--theme-primary);
  color: var(--theme-primary);
  background: transparent;
  font-size: 15px;
  transition: all 0.3s ease;
}

.register-btn:hover {
  background: var(--theme-primary);
  color: white;
}

.login-footer {
  padding: 20px 24px 40px;
  text-align: center;
}

.footer-text {
  font-size: 12px;
  color: var(--theme-text-secondary);
  margin: 0 0 8px 0;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--theme-text-secondary);
}

.link-text {
  color: var(--theme-primary);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.link-text:hover {
  opacity: 0.7;
}

/* 主题特定样式 */
.theme-wechat .login-container {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.theme-dark .login-container {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.theme-business .login-container {
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
}

@media (max-width: 480px) {
  .login-header {
    padding: 40px 20px 30px;
  }

  .app-title {
    font-size: 32px;
  }

  .login-form {
    padding: 28px 20px 20px;
    border-radius: 16px 16px 0 0;
  }

  .input-group {
    margin-bottom: 12px;
  }

  .login-input {
    padding: 14px;
  }

  .login-btn {
    height: 46px;
  }
}

@media (max-height: 700px) {
  .login-header {
    flex: 0;
    padding: 40px 20px 20px;
  }

  .app-logo {
    margin-bottom: 12px;
  }

  .app-title {
    font-size: 28px;
  }
}
</style>
