<template>
  <div class="friend-requests-container">
    <van-nav-bar
      title="新的朋友"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <div class="requests-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <!-- 搜索入口 -->
        <van-cell-group>
          <van-cell
            title="搜索用户"
            icon="search"
            is-link
            @click="goToSearch"
            class="search-cell"
          >
            <template #right-icon>
              <van-icon name="arrow" />
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 好友申请列表 -->
        <div class="requests-section">
          <div v-if="isLoading" class="loading-state">
            <van-loading size="24px" />
            <span>加载中...</span>
          </div>

          <div v-else-if="friendRequests.length === 0" class="empty-state">
            <van-empty description="暂无好友申请">
              <van-button type="primary" size="small" @click="goToSearch">
                搜索添加好友
              </van-button>
            </van-empty>
          </div>

          <van-cell-group v-else>
            <van-cell
              v-for="request in friendRequests"
              :key="request.id"
              :title="request.requester.displayName"
              :label="`用户名: ${request.requester.username}`"
              class="request-item"
            >
              <template #icon>
                <van-image
                  :src="request.requester.avatar || defaultAvatar"
                  round
                  width="40"
                  height="40"
                  fit="cover"
                  class="requester-avatar"
                />
              </template>

              <template #right-icon>
                <div class="request-actions">
                  <van-button
                    type="danger"
                    size="small"
                    @click="rejectRequest(request.requester.id)"
                    :loading="rejecting === request.requester.id"
                    class="action-btn reject-btn"
                  >
                    拒绝
                  </van-button>
                  <van-button
                    type="primary"
                    size="small"
                    @click="acceptRequest(request.requester.id)"
                    :loading="accepting === request.requester.id"
                    class="action-btn accept-btn"
                  >
                    接受
                  </van-button>
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>

        <!-- 申请时间显示 -->
        <div v-if="friendRequests.length > 0" class="requests-footer">
          <p class="footer-text">
            共 {{ friendRequests.length }} 条好友申请
          </p>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useFriendsStore } from '@/stores/friends'

const router = useRouter()
const friendsStore = useFriendsStore()

const refreshing = ref(false)
const accepting = ref<number | null>(null)
const rejecting = ref<number | null>(null)
const defaultAvatar = 'https://img.yzcdn.cn/vant/cat.jpeg'

const friendRequests = computed(() => friendsStore.friendRequests)
const isLoading = computed(() => friendsStore.isLoading)

// 刷新好友申请
const onRefresh = async () => {
  try {
    await friendsStore.fetchFriendRequests()
  } catch (error) {
    console.error('刷新好友申请失败:', error)
    showToast.fail('刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 接受好友申请
const acceptRequest = async (requesterId: number) => {
  accepting.value = requesterId
  try {
    await friendsStore.acceptFriendRequest(requesterId)
    showToast.success('已接受好友申请')
  } catch (error: any) {
    console.error('接受好友申请失败:', error)
    showToast.fail(error.response?.data?.message || '操作失败')
  } finally {
    accepting.value = null
  }
}

// 拒绝好友申请
const rejectRequest = async (requesterId: number) => {
  rejecting.value = requesterId
  try {
    await friendsStore.rejectFriendRequest(requesterId)
    showToast.success('已拒绝好友申请')
  } catch (error: any) {
    console.error('拒绝好友申请失败:', error)
    showToast.fail(error.response?.data?.message || '操作失败')
  } finally {
    rejecting.value = null
  }
}

// 导航
const goBack = () => {
  router.back()
}

const goToSearch = () => {
  router.push('/contacts/search')
}

onMounted(async () => {
  await onRefresh()
})
</script>

<style scoped>
.friend-requests-container {
  height: 100vh;
  background: var(--theme-background);
}

.requests-content {
  padding-top: 8px;
}

.search-cell {
  background: var(--theme-surface);
  margin-bottom: 8px;
}

.requests-section {
  margin-top: 8px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: var(--theme-text-secondary);
  gap: 12px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.request-item {
  background: var(--theme-surface);
  margin-bottom: 1px;
}

.requester-avatar {
  margin-right: 12px;
}

.request-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  min-width: 60px;
  height: 32px;
  font-size: 13px;
}

.reject-btn {
  background: #f56c6c;
  border-color: #f56c6c;
}

.accept-btn {
  background: var(--theme-primary);
  border-color: var(--theme-primary);
}

.requests-footer {
  padding: 16px 20px;
  text-align: center;
}

.footer-text {
  font-size: 12px;
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 主题适配 */
:deep(.van-cell-group) {
  background: var(--theme-surface);
}

:deep(.van-cell) {
  background: var(--theme-surface);
  color: var(--theme-text);
}

:deep(.van-cell__title) {
  color: var(--theme-text);
}

:deep(.van-cell__label) {
  color: var(--theme-text-secondary);
}

@media (max-width: 480px) {
  .request-actions {
    gap: 6px;
  }
  
  .action-btn {
    min-width: 50px;
    height: 28px;
    font-size: 12px;
  }
}
</style>
