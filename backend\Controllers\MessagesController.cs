using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ChatApp.Data;
using ChatApp.Models;
using System.Security.Claims;

namespace ChatApp.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MessagesController : ControllerBase
    {
        private readonly ChatDbContext _context;

        public MessagesController(ChatDbContext context)
        {
            _context = context;
        }

        // 获取私聊历史消息
        [HttpGet("private/{friendId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetPrivateMessages(int friendId, int page = 1, int pageSize = 50)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);

            // 验证好友关系
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => 
                    ((f.RequesterId == userId && f.AddresseeId == friendId) ||
                     (f.RequesterId == friendId && f.AddresseeId == userId)) &&
                    f.Status == FriendshipStatus.Accepted);

            if (friendship == null)
            {
                return Forbid("只能查看好友的聊天记录");
            }

            var messages = await _context.Messages
                .Include(m => m.Sender)
                .Where(m => 
                    (m.SenderId == userId && m.ReceiverId == friendId) ||
                    (m.SenderId == friendId && m.ReceiverId == userId))
                .OrderByDescending(m => m.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new
                {
                    id = m.Id,
                    senderId = m.SenderId,
                    receiverId = m.ReceiverId,
                    content = m.Content,
                    type = "private",
                    createdAt = m.CreatedAt,
                    isRead = m.IsRead,
                    sender = new
                    {
                        id = m.Sender.Id,
                        username = m.Sender.Username,
                        displayName = m.Sender.DisplayName,
                        avatar = m.Sender.Avatar
                    }
                })
                .ToListAsync();

            // 反转顺序，使最新消息在最后
            messages.Reverse();

            return Ok(messages);
        }

        // 获取群聊历史消息
        [HttpGet("group/{groupId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetGroupMessages(int groupId, int page = 1, int pageSize = 50)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);

            // 验证群成员身份
            var membership = await _context.GroupMembers
                .FirstOrDefaultAsync(gm => gm.GroupId == groupId && gm.UserId == userId);

            if (membership == null)
            {
                return Forbid("只能查看所在群组的聊天记录");
            }

            var messages = await _context.Messages
                .Include(m => m.Sender)
                .Where(m => m.GroupId == groupId)
                .OrderByDescending(m => m.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new
                {
                    id = m.Id,
                    senderId = m.SenderId,
                    groupId = m.GroupId,
                    content = m.Content,
                    type = "group",
                    createdAt = m.CreatedAt,
                    isRead = m.IsRead,
                    sender = new
                    {
                        id = m.Sender.Id,
                        username = m.Sender.Username,
                        displayName = m.Sender.DisplayName,
                        avatar = m.Sender.Avatar
                    }
                })
                .ToListAsync();

            // 反转顺序，使最新消息在最后
            messages.Reverse();

            return Ok(messages);
        }

        // 标记消息为已读
        [HttpPost("mark-read")]
        public async Task<IActionResult> MarkMessagesAsRead([FromBody] MarkReadRequest request)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);

            if (request.Type == "private")
            {
                // 标记私聊消息为已读
                var messages = await _context.Messages
                    .Where(m => m.SenderId == request.SenderId && m.ReceiverId == userId && !m.IsRead)
                    .ToListAsync();

                foreach (var message in messages)
                {
                    message.IsRead = true;
                    message.ReadAt = DateTime.UtcNow;
                }
            }
            else if (request.Type == "group" && request.GroupId.HasValue)
            {
                // 标记群聊消息为已读
                var messages = await _context.Messages
                    .Where(m => m.GroupId == request.GroupId && m.ReceiverId == userId && !m.IsRead)
                    .ToListAsync();

                foreach (var message in messages)
                {
                    message.IsRead = true;
                    message.ReadAt = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();
            return Ok();
        }

        // 获取未读消息数量
        [HttpGet("unread-count")]
        public async Task<ActionResult<object>> GetUnreadCount()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);

            var unreadCount = await _context.Messages
                .Where(m => m.ReceiverId == userId && !m.IsRead)
                .CountAsync();

            return Ok(new { unreadCount });
        }
    }

    public class MarkReadRequest
    {
        public string Type { get; set; } = string.Empty; // "private" or "group"
        public int SenderId { get; set; }
        public int? GroupId { get; set; }
    }
}
