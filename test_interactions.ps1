# 实时通讯项目交互测试脚本

Write-Host "=== 实时通讯项目交互测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:5057/api"
$frontendUrl = "http://localhost:5174"

# 测试1: 验证前端页面加载
Write-Host "`n1. 测试前端页面加载..." -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri $frontendUrl -UseBasicParsing
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✓ 前端页面加载成功" -ForegroundColor Green
        
        # 检查是否包含Vue默认内容
        if ($frontendResponse.Content -match "You did it!") {
            Write-Host "✗ 仍包含Vue默认内容" -ForegroundColor Red
        } else {
            Write-Host "✓ 已清除Vue默认内容" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "✗ 前端页面加载失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 用户登录获取token
Write-Host "`n2. 测试用户登录..." -ForegroundColor Yellow
try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -ContentType "application/json" -Body '{"username":"testuser","password":"123456"}'
    Write-Host "✓ 用户登录成功: $($loginResponse.user.displayName)" -ForegroundColor Green
    $token = $loginResponse.token
    $headers = @{Authorization = "Bearer $token"}
} catch {
    Write-Host "✗ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试3: 获取好友列表
Write-Host "`n3. 测试获取好友列表..." -ForegroundColor Yellow
try {
    $friendsResponse = Invoke-RestMethod -Uri "$baseUrl/friends" -Method GET -Headers $headers
    Write-Host "✓ 获取好友列表成功，共 $($friendsResponse.Count) 个好友" -ForegroundColor Green
    
    foreach ($friend in $friendsResponse) {
        Write-Host "  - $($friend.displayName) (在线: $($friend.isOnline))" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 获取好友列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 获取好友申请
Write-Host "`n4. 测试获取好友申请..." -ForegroundColor Yellow
try {
    $requestsResponse = Invoke-RestMethod -Uri "$baseUrl/friends/requests" -Method GET -Headers $headers
    Write-Host "✓ 获取好友申请成功，共 $($requestsResponse.Count) 个申请" -ForegroundColor Green
    
    foreach ($request in $requestsResponse) {
        Write-Host "  - 来自: $($request.requester.displayName) (用户名: $($request.requester.username))" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 获取好友申请失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 搜索用户
Write-Host "`n5. 测试搜索用户..." -ForegroundColor Yellow
try {
    $searchResponse = Invoke-RestMethod -Uri "$baseUrl/friends/search" -Method POST -ContentType "application/json" -Body '{"query":"testuser2"}' -Headers $headers
    Write-Host "✓ 搜索用户成功，找到 $($searchResponse.Count) 个用户" -ForegroundColor Green
    
    foreach ($user in $searchResponse) {
        Write-Host "  - $($user.displayName) (状态: $($user.friendshipStatus))" -ForegroundColor Cyan
    }
} catch {
    Write-Host "✗ 搜索用户失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试6: 测试SignalR连接端点
Write-Host "`n6. 测试SignalR连接端点..." -ForegroundColor Yellow
try {
    $signalrResponse = Invoke-WebRequest -Uri "http://localhost:5057/chatHub/negotiate?negotiateVersion=1" -UseBasicParsing -Headers $headers
    if ($signalrResponse.StatusCode -eq 200) {
        Write-Host "✓ SignalR negotiate端点正常" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ SignalR连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试7: 测试前端路由
Write-Host "`n7. 测试前端路由..." -ForegroundColor Yellow
$routes = @(
    "/login",
    "/register", 
    "/contacts",
    "/contacts/search",
    "/contacts/requests",
    "/groups"
)

foreach ($route in $routes) {
    try {
        $routeResponse = Invoke-WebRequest -Uri "$frontendUrl$route" -UseBasicParsing
        if ($routeResponse.StatusCode -eq 200) {
            Write-Host "✓ 路由 $route 正常" -ForegroundColor Green
        }
    } catch {
        Write-Host "✗ 路由 $route 失败" -ForegroundColor Red
    }
}

# 测试8: 创建测试用户进行完整流程测试
Write-Host "`n8. 创建测试用户..." -ForegroundColor Yellow
$testUsername = "testuser_$(Get-Date -Format 'HHmmss')"
try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/auth/register" -Method POST -ContentType "application/json" -Body "{`"username`":`"$testUsername`",`"email`":`"$<EMAIL>`",`"password`":`"123456`",`"displayName`":`"测试用户_$(Get-Date -Format 'HHmmss')`"}"
    Write-Host "✓ 测试用户创建成功: $($registerResponse.user.displayName)" -ForegroundColor Green
    $testToken = $registerResponse.token
    $testHeaders = @{Authorization = "Bearer $testToken"}
    
    # 使用新用户搜索现有用户
    Write-Host "`n9. 测试新用户搜索功能..." -ForegroundColor Yellow
    $searchResponse2 = Invoke-RestMethod -Uri "$baseUrl/friends/search" -Method POST -ContentType "application/json" -Body '{"query":"testuser"}' -Headers $testHeaders
    Write-Host "✓ 新用户搜索成功，找到 $($searchResponse2.Count) 个用户" -ForegroundColor Green
    
    if ($searchResponse2.Count -gt 0) {
        $targetUser = $searchResponse2[0]
        Write-Host "`n10. 测试发送好友请求..." -ForegroundColor Yellow
        try {
            $friendRequestResponse = Invoke-RestMethod -Uri "$baseUrl/friends/request/$($targetUser.id)" -Method POST -Headers $testHeaders
            Write-Host "✓ 好友请求发送成功" -ForegroundColor Green
        } catch {
            Write-Host "✗ 发送好友请求失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "✗ 创建测试用户失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试总结 ===" -ForegroundColor Green
Write-Host "前端应用: $frontendUrl" -ForegroundColor Cyan
Write-Host "后端API: http://localhost:5057" -ForegroundColor Cyan

Write-Host "`n🎯 手动测试步骤:" -ForegroundColor Yellow
Write-Host "1. 访问 $frontendUrl" -ForegroundColor Cyan
Write-Host "2. 点击右上角调色板图标测试主题切换" -ForegroundColor Cyan
Write-Host "3. 使用 testuser/123456 登录" -ForegroundColor Cyan
Write-Host "4. 测试联系人 -> 新的朋友 -> 查看好友申请" -ForegroundColor Cyan
Write-Host "5. 测试搜索用户并添加好友" -ForegroundColor Cyan
Write-Host "6. 测试私聊和群聊功能" -ForegroundColor Cyan

Write-Host "`n✨ 主要修复内容:" -ForegroundColor Yellow
Write-Host "✓ 修复了SignalR CORS配置" -ForegroundColor Green
Write-Host "✓ 创建了好友申请页面 (/contacts/requests)" -ForegroundColor Green
Write-Host "✓ 清除了所有Vue默认样式" -ForegroundColor Green
Write-Host "✓ 实现了完整的主题切换系统" -ForegroundColor Green
Write-Host "✓ 优化了移动端适配" -ForegroundColor Green

Write-Host "`n🚀 现在可以进行完整的功能测试！" -ForegroundColor Green
