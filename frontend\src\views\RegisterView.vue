<template>
  <div class="register-container">
    <van-nav-bar
      title="注册账号"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <div class="register-content">
      <div class="register-header">
        <h2>创建新账号</h2>
        <p>加入我们，开始聊天之旅</p>
      </div>

      <van-form @submit="handleRegister">
        <van-cell-group inset>
          <van-field
            v-model="form.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="usernameRules"
            left-icon="user-o"
          />
          <van-field
            v-model="form.email"
            name="email"
            label="邮箱"
            placeholder="请输入邮箱地址"
            :rules="emailRules"
            left-icon="envelop-o"
          />
          <van-field
            v-model="form.displayName"
            name="displayName"
            label="昵称"
            placeholder="请输入显示昵称（可选）"
            left-icon="contact"
          />
          <van-field
            v-model="form.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            :rules="passwordRules"
            left-icon="lock"
          />
          <van-field
            v-model="form.confirmPassword"
            type="password"
            name="confirmPassword"
            label="确认密码"
            placeholder="请再次输入密码"
            :rules="confirmPasswordRules"
            left-icon="lock"
          />
        </van-cell-group>

        <div class="register-actions">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="isLoading"
            loading-text="注册中..."
          >
            注册
          </van-button>
        </div>
      </van-form>

      <div class="login-link">
        <span>已有账号？</span>
        <van-button type="primary" plain size="small" @click="goToLogin">
          立即登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const isLoading = ref(false)
const form = reactive({
  username: '',
  email: '',
  displayName: '',
  password: '',
  confirmPassword: ''
})

// 验证规则
const usernameRules = [
  { required: true, message: '请输入用户名' },
  { min: 3, max: 20, message: '用户名长度为3-20个字符' },
  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
]

const emailRules = [
  { required: true, message: '请输入邮箱地址' },
  { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱格式' }
]

const passwordRules = [
  { required: true, message: '请输入密码' },
  { min: 6, message: '密码长度至少6个字符' }
]

const confirmPasswordRules = [
  { required: true, message: '请确认密码' },
  {
    validator: (value: string) => {
      if (value !== form.password) {
        return '两次输入的密码不一致'
      }
      return true
    }
  }
]

const handleRegister = async () => {
  if (!form.username || !form.email || !form.password || !form.confirmPassword) {
    showToast('请填写完整信息')
    return
  }

  if (form.password !== form.confirmPassword) {
    showToast('两次输入的密码不一致')
    return
  }

  isLoading.value = true
  try {
    await authStore.register(
      form.username,
      form.email,
      form.password,
      form.displayName || undefined
    )
    showToast.success('注册成功')
    router.push('/')
  } catch (error: any) {
    console.error('注册失败:', error)
    showToast.fail(error.response?.data?.message || '注册失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

const goBack = () => {
  router.back()
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: var(--theme-background);
}

.register-content {
  padding: 20px;
  padding-top: 80px;
  background: var(--theme-background);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
  color: var(--theme-text);
}

.register-header h2 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--theme-text);
}

.register-header p {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
  color: var(--theme-text-secondary);
}

.register-actions {
  margin-top: 24px;
}

.login-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: var(--theme-text-secondary);
}

.login-link span {
  margin-right: 8px;
}

/* 表单样式优化 */
:deep(.van-cell-group) {
  background: var(--theme-surface);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.van-field) {
  background: var(--theme-surface);
  border-bottom: 1px solid var(--theme-border);
}

:deep(.van-field:last-child) {
  border-bottom: none;
}

:deep(.van-field__control) {
  color: var(--theme-text);
}

:deep(.van-field__label) {
  color: var(--theme-text);
}

:deep(.van-button--primary) {
  background: var(--theme-primary);
  border-color: var(--theme-primary);
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
}

@media (max-width: 480px) {
  .register-content {
    padding: 16px;
    padding-top: 76px;
  }
}
</style>
