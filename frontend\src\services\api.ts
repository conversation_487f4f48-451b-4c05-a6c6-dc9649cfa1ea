import axios from 'axios'

const API_BASE_URL = 'http://localhost:5057/api'

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，清除本地存储并跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 登录
  login: (username: string, password: string) =>
    api.post('/auth/login', { username, password }),
  
  // 注册
  register: (username: string, email: string, password: string, displayName?: string) =>
    api.post('/auth/register', { username, email, password, displayName })
}

// 好友相关API
export const friendsAPI = {
  // 获取好友列表
  getFriends: () => api.get('/friends'),
  
  // 获取好友请求
  getPendingRequests: () => api.get('/friends/requests'),
  
  // 搜索用户
  searchUsers: (query: string) => api.post('/friends/search', { query }),
  
  // 发送好友请求
  sendFriendRequest: (userId: number) => api.post(`/friends/request/${userId}`),
  
  // 接受好友请求
  acceptFriendRequest: (requesterId: number) => api.post(`/friends/accept/${requesterId}`),
  
  // 拒绝好友请求
  rejectFriendRequest: (requesterId: number) => api.post(`/friends/reject/${requesterId}`),
  
  // 拉黑用户
  blockUser: (userId: number) => api.post(`/friends/block/${userId}`),
  
  // 解除拉黑
  unblockUser: (userId: number) => api.post(`/friends/unblock/${userId}`),
  
  // 删除好友
  removeFriend: (userId: number) => api.delete(`/friends/${userId}`)
}

// 群聊相关API (模拟)
export const groupsAPI = {
  // 获取群聊列表
  getGroups: () => api.get('/groups'),

  // 创建群聊
  createGroup: (name: string, description?: string, memberIds?: number[]) =>
    api.post('/groups', { name, description, memberIds }),

  // 获取群聊详情
  getGroupDetail: (groupId: number) => api.get(`/groups/${groupId}`),

  // 加入群聊
  joinGroup: (groupId: number) => api.post(`/groups/${groupId}/join`),

  // 退出群聊
  leaveGroup: (groupId: number) => api.post(`/groups/${groupId}/leave`),

  // 获取群成员
  getGroupMembers: (groupId: number) => api.get(`/groups/${groupId}/members`),

  // 添加群成员
  addGroupMember: (groupId: number, userId: number) =>
    api.post(`/groups/${groupId}/members`, { userId }),

  // 移除群成员
  removeGroupMember: (groupId: number, userId: number) =>
    api.delete(`/groups/${groupId}/members/${userId}`)
}

// 消息相关API
export const messageAPI = {
  // 获取私聊历史消息
  getPrivateMessages: (friendId: number, page = 1, pageSize = 50) =>
    api.get(`/messages/private/${friendId}?page=${page}&pageSize=${pageSize}`),

  // 获取群聊历史消息
  getGroupMessages: (groupId: number, page = 1, pageSize = 50) =>
    api.get(`/messages/group/${groupId}?page=${page}&pageSize=${pageSize}`),

  // 标记消息为已读
  markAsRead: (data: { type: string; senderId?: number; groupId?: number }) =>
    api.post('/messages/mark-read', data),

  // 获取未读消息数量
  getUnreadCount: () => api.get('/messages/unread-count')
}

export default api
