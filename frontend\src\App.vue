<script setup lang="ts">
import { RouterView } from 'vue-router'
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--theme-background, #f7f8fa);
  color: var(--theme-text, #323233);
}

#app {
  height: 100%;
  overflow: hidden;
}

/* 移除默认的链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 移除默认的按钮样式 */
button {
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
}

/* 移除默认的输入框样式 */
input, textarea {
  border: none;
  outline: none;
  background: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 主题变量默认值 */
:root {
  --theme-primary: #07c160;
  --theme-background: #f7f8fa;
  --theme-surface: #ffffff;
  --theme-text: #323233;
  --theme-text-secondary: #969799;
  --theme-border: #ebedf0;
  --theme-success: #07c160;
  --theme-warning: #ff976a;
  --theme-danger: #ee0a24;
  --theme-chat-bubble-self: #95ec69;
  --theme-chat-bubble-other: #ffffff;
  --theme-nav-bar: #f7f7f7;
  --theme-tab-bar: #f7f7f7;
}

/* 禁用文本选择（除了输入框和消息内容） */
body {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许特定元素的文本选择 */
input, textarea, .message-text, .van-field__control {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 移动端优化 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

/* 防止iOS缩放 */
@media screen and (max-width: 480px) {
  input[type="text"],
  input[type="password"],
  input[type="email"],
  textarea {
    font-size: 16px !important;
  }
}
</style>
