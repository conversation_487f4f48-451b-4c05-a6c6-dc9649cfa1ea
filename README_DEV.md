# 开发环境配置指南

## 项目概述

这是一个基于 .NET Core 6.0 + SignalR 和 Vue.js 的实时通讯应用，类似微信的功能和界面设计。

## 技术栈

### 后端
- .NET Core 6.0
- SignalR (实时通讯)
- Entity Framework Core (SQLite)
- JWT Authentication
- BCrypt (密码加密)

### 前端
- Vue.js 3 + TypeScript
- Vite (构建工具)
- Pinia (状态管理)
- Vue Router (路由)
- Vant (移动端UI组件库)
- Axios (HTTP客户端)
- SignalR Client

## 开发环境要求

### 后端
- .NET 6.0 SDK 或更高版本
- Visual Studio 2022 或 VS Code

### 前端
- Node.js 16+ 
- npm 或 yarn

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd 通讯
```

### 2. 启动后端
```bash
cd backend
dotnet restore
dotnet run
```
后端将在 http://localhost:5057 启动

### 3. 启动前端
```bash
cd frontend
npm install
npm run dev
```
前端将在 http://localhost:5174 启动

## 项目结构

```
通讯/
├── backend/                 # 后端项目
│   ├── Controllers/         # API控制器
│   │   ├── AuthController.cs
│   │   └── FriendsController.cs
│   ├── Hubs/               # SignalR Hub
│   │   └── ChatHub.cs
│   ├── Models/             # 数据模型
│   │   ├── User.cs
│   │   ├── Friendship.cs
│   │   ├── Message.cs
│   │   └── Group.cs
│   ├── Services/           # 业务逻辑服务
│   │   ├── JwtService.cs
│   │   ├── UserService.cs
│   │   └── FriendshipService.cs
│   ├── Data/               # 数据访问层
│   │   └── ChatDbContext.cs
│   ├── Program.cs          # 应用程序入口
│   └── appsettings.json    # 配置文件
├── frontend/               # 前端项目
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   ├── views/          # 页面视图
│   │   │   ├── LoginView.vue
│   │   │   ├── RegisterView.vue
│   │   │   ├── HomeView.vue
│   │   │   ├── ChatView.vue
│   │   │   ├── ContactsView.vue
│   │   │   ├── SearchView.vue
│   │   │   └── ProfileView.vue
│   │   ├── stores/         # Pinia状态管理
│   │   │   ├── auth.ts
│   │   │   ├── chat.ts
│   │   │   └── friends.ts
│   │   ├── services/       # API服务
│   │   │   ├── api.ts
│   │   │   └── signalr.ts
│   │   ├── styles/         # 样式文件
│   │   │   └── mobile.css
│   │   ├── utils/          # 工具函数
│   │   │   └── mobile.ts
│   │   ├── router/         # 路由配置
│   │   │   └── index.ts
│   │   └── main.ts         # 应用程序入口
│   ├── index.html
│   └── package.json
└── README.md
```

## 功能特性

### 已实现功能
- ✅ 用户注册/登录 (JWT认证)
- ✅ 实时私聊 (SignalR)
- ✅ 好友管理 (添加/删除/拉黑)
- ✅ 好友搜索
- ✅ 移动端适配
- ✅ 响应式设计

### 待实现功能
- ⏳ 群聊功能
- ⏳ 消息历史记录
- ⏳ 文件/图片发送
- ⏳ 消息已读状态
- ⏳ 推送通知

## API接口

### 认证接口
- POST `/api/auth/login` - 用户登录
- POST `/api/auth/register` - 用户注册

### 好友接口
- GET `/api/friends` - 获取好友列表
- GET `/api/friends/requests` - 获取好友请求
- POST `/api/friends/search` - 搜索用户
- POST `/api/friends/request/{userId}` - 发送好友请求
- POST `/api/friends/accept/{requesterId}` - 接受好友请求
- POST `/api/friends/reject/{requesterId}` - 拒绝好友请求
- POST `/api/friends/block/{userId}` - 拉黑用户
- DELETE `/api/friends/{userId}` - 删除好友

### SignalR Hub
- `/chatHub` - 实时通讯Hub
  - `SendPrivateMessage(receiverId, content)` - 发送私聊消息
  - `SendGroupMessage(groupId, content)` - 发送群聊消息
  - `JoinGroup(groupId)` - 加入群聊
  - `LeaveGroup(groupId)` - 离开群聊

## 数据库

项目使用SQLite数据库，数据库文件为 `chat.db`，包含以下表：
- Users - 用户表
- Friendships - 好友关系表
- Messages - 消息表
- Groups - 群组表
- GroupMembers - 群组成员表

## 开发调试

### 后端调试
1. 在Visual Studio或VS Code中打开backend项目
2. 设置断点
3. 按F5启动调试

### 前端调试
1. 在浏览器中打开开发者工具
2. 使用Vue DevTools扩展
3. 查看Network面板监控API请求

### SignalR调试
1. 在浏览器控制台查看SignalR连接状态
2. 使用SignalR客户端日志
3. 在后端Hub方法中设置断点

## 部署配置

### 后端部署
1. 发布应用：`dotnet publish -c Release`
2. 配置生产环境数据库连接字符串
3. 配置HTTPS证书
4. 部署到IIS或Linux服务器

### 前端部署
1. 构建生产版本：`npm run build`
2. 将dist文件夹内容部署到Web服务器
3. 配置nginx或Apache反向代理

## 常见问题

### 1. SignalR连接失败
- 检查CORS配置
- 确认JWT token有效
- 检查网络连接

### 2. 移动端样式问题
- 检查viewport设置
- 确认CSS媒体查询
- 测试不同设备尺寸

### 3. 数据库连接问题
- 检查连接字符串
- 确认数据库文件权限
- 运行数据库迁移

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
