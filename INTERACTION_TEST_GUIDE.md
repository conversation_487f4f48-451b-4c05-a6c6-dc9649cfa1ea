# 🧪 交互功能测试指南

## 测试环境状态
- **前端**: http://localhost:5174 ✅ 运行中
- **后端**: http://localhost:5057 ✅ 运行中
- **数据库**: SQLite ✅ 正常工作

## 🎯 完整功能测试流程

### 1. 样式和主题测试
**测试步骤:**
1. 访问 http://localhost:5174
2. 点击登录页面右上角的调色板图标
3. 验证主题切换功能（4种主题）
4. 检查移动端响应式设计

**预期结果:**
- ✅ 无Vue模板默认样式
- ✅ 微信风格界面
- ✅ 主题实时切换
- ✅ 移动端适配正常

### 2. 用户注册测试
**测试步骤:**
1. 点击"注册新账号"按钮
2. 填写注册信息：
   - 用户名: testuser4
   - 邮箱: <EMAIL>
   - 密码: 123456
   - 昵称: 测试用户4
3. 点击"注册"按钮

**预期结果:**
- ✅ 注册成功并自动登录
- ✅ 跳转到主页面

### 3. 用户登录测试
**测试步骤:**
1. 使用已有账号登录：
   - 用户名: testuser
   - 密码: 123456
2. 点击"登录"按钮

**预期结果:**
- ✅ 登录成功
- ✅ 跳转到聊天列表页面

### 4. 搜索和添加好友测试
**测试步骤:**
1. 点击底部"联系人"标签
2. 点击"新的朋友"
3. 点击"搜索用户"
4. 搜索: testuser2
5. 点击"添加"按钮发送好友请求

**预期结果:**
- ✅ 搜索结果正确显示
- ✅ 好友请求发送成功
- ✅ 按钮状态变为"已发送"

### 5. 好友申请处理测试
**测试步骤:**
1. 使用testuser2账号登录
2. 进入"联系人" -> "新的朋友"
3. 查看好友申请列表
4. 点击"接受"按钮

**预期结果:**
- ✅ 显示好友申请
- ✅ 接受成功
- ✅ 申请从列表中消失

### 6. 私聊功能测试
**测试步骤:**
1. 在好友列表中点击好友
2. 进入聊天界面
3. 发送消息: "你好，这是测试消息"
4. 验证实时消息传递

**预期结果:**
- ✅ 聊天界面正常显示
- ✅ 消息发送成功
- ✅ 消息气泡样式正确

### 7. 群聊管理测试
**测试步骤:**
1. 点击"联系人" -> "群聊"
2. 点击右上角"+"创建群聊
3. 输入群名称: "测试群聊"
4. 点击"创建群聊"
5. 进入群聊发送消息

**预期结果:**
- ✅ 群聊创建成功
- ✅ 群聊列表显示
- ✅ 群聊消息正常

### 8. 拉黑功能测试
**测试步骤:**
1. 在好友列表中长按或点击好友
2. 选择"拉黑"选项
3. 确认拉黑操作
4. 验证好友从列表中移除

**预期结果:**
- ✅ 拉黑操作成功
- ✅ 好友列表更新

## 🔧 SignalR连接测试

### 检查SignalR状态
1. 打开浏览器开发者工具
2. 查看Network标签
3. 验证WebSocket连接状态
4. 检查是否有negotiate请求

**正常状态:**
- ✅ negotiate请求成功
- ✅ WebSocket连接建立
- ✅ 实时消息推送正常

## 🐛 问题排查

### 如果好友申请页面无法加载
1. 检查路由配置: `/contacts/requests`
2. 验证API调用: `GET /api/friends/requests`
3. 检查认证token是否有效

### 如果SignalR连接失败
1. 检查CORS配置
2. 验证JWT token
3. 查看浏览器控制台错误

### 如果样式显示异常
1. 清除浏览器缓存
2. 检查CSS变量是否正确应用
3. 验证主题切换功能

## 📱 移动端测试

### 在手机浏览器中测试
1. 使用手机访问 http://localhost:5174
2. 测试触摸交互
3. 验证响应式布局
4. 检查安全区域适配

## 🎯 测试检查清单

### 基础功能
- [ ] 页面加载正常
- [ ] 主题切换功能
- [ ] 用户注册
- [ ] 用户登录
- [ ] 搜索用户
- [ ] 发送好友请求
- [ ] 接受好友请求
- [ ] 私聊消息
- [ ] 群聊功能
- [ ] 拉黑用户

### 界面体验
- [ ] 微信风格设计
- [ ] 移动端适配
- [ ] 动画效果
- [ ] 加载状态
- [ ] 错误提示

### 实时功能
- [ ] SignalR连接
- [ ] 消息实时推送
- [ ] 在线状态显示
- [ ] 好友状态变化

## 🚀 快速测试命令

```bash
# 测试后端API
curl http://localhost:5057/api/auth/login -Method POST -ContentType "application/json" -Body '{"username":"testuser","password":"123456"}'

# 测试前端页面
curl http://localhost:5174
```

## 📊 测试结果记录

请在测试过程中记录以下信息：
- 功能是否正常工作
- 界面显示是否正确
- 性能表现如何
- 发现的问题和建议

完成测试后，所有功能应该都能正常工作，界面应该呈现专业的微信风格设计！
