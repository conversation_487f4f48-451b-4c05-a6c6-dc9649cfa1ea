using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using ChatApp.Data;
using ChatApp.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace ChatApp.Hubs
{
    [Authorize]
    public class ChatHub : Hub
    {
        private readonly ChatDbContext _context;
        private static readonly Dictionary<string, string> _connections = new();

        public ChatHub(ChatDbContext context)
        {
            _context = context;
        }

        public override async Task OnConnectedAsync()
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId != null)
            {
                _connections[Context.ConnectionId] = userId;
                
                // 更新用户在线状态
                var user = await _context.Users.FindAsync(int.Parse(userId));
                if (user != null)
                {
                    user.IsOnline = true;
                    user.LastSeen = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                // 通知好友用户上线
                await NotifyFriendsStatusChange(int.Parse(userId), true);
            }
            
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            if (_connections.TryGetValue(Context.ConnectionId, out var userId))
            {
                _connections.Remove(Context.ConnectionId);
                
                // 更新用户离线状态
                var user = await _context.Users.FindAsync(int.Parse(userId));
                if (user != null)
                {
                    user.IsOnline = false;
                    user.LastSeen = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                // 通知好友用户离线
                await NotifyFriendsStatusChange(int.Parse(userId), false);
            }
            
            await base.OnDisconnectedAsync(exception);
        }

        // 发送私聊消息
        public async Task SendPrivateMessage(int receiverId, string content)
        {
            var senderId = int.Parse(Context.User!.FindFirst(ClaimTypes.NameIdentifier)!.Value);
            
            // 检查是否为好友关系
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => 
                    ((f.RequesterId == senderId && f.AddresseeId == receiverId) ||
                     (f.RequesterId == receiverId && f.AddresseeId == senderId)) &&
                    f.Status == FriendshipStatus.Accepted);
            
            if (friendship == null)
            {
                await Clients.Caller.SendAsync("Error", "只能向好友发送消息");
                return;
            }

            var message = new Message
            {
                SenderId = senderId,
                ReceiverId = receiverId,
                Content = content,
                Type = MessageType.Text,
                CreatedAt = DateTime.UtcNow
            };

            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            // 获取发送者信息
            var sender = await _context.Users.FindAsync(senderId);
            
            var messageData = new
            {
                id = message.Id,
                senderId = message.SenderId,
                receiverId = message.ReceiverId,
                content = message.Content,
                type = "private",
                createdAt = message.CreatedAt,
                isRead = message.IsRead,
                sender = new
                {
                    id = sender?.Id ?? senderId,
                    username = sender?.Username ?? "",
                    displayName = sender?.DisplayName ?? "",
                    avatar = sender?.Avatar
                }
            };

            // 发送给接收者
            var receiverConnectionId = _connections.FirstOrDefault(c => c.Value == receiverId.ToString()).Key;
            if (receiverConnectionId != null)
            {
                await Clients.Client(receiverConnectionId).SendAsync("ReceiveMessage", messageData);
            }

            // 确认发送给发送者
            await Clients.Caller.SendAsync("MessageSent", messageData);
        }

        // 发送群聊消息
        public async Task SendGroupMessage(int groupId, string content)
        {
            var senderId = int.Parse(Context.User!.FindFirst(ClaimTypes.NameIdentifier)!.Value);
            
            // 检查用户是否为群成员
            var membership = await _context.GroupMembers
                .FirstOrDefaultAsync(gm => gm.GroupId == groupId && gm.UserId == senderId);
            
            if (membership == null)
            {
                await Clients.Caller.SendAsync("Error", "您不是该群的成员");
                return;
            }

            var message = new Message
            {
                SenderId = senderId,
                GroupId = groupId,
                Content = content,
                Type = MessageType.Text,
                CreatedAt = DateTime.UtcNow
            };

            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            // 获取发送者信息
            var sender = await _context.Users.FindAsync(senderId);
            
            var messageData = new
            {
                Id = message.Id,
                SenderId = senderId,
                SenderName = sender?.DisplayName ?? sender?.Username,
                Content = content,
                CreatedAt = message.CreatedAt,
                GroupId = groupId,
                Type = "group"
            };

            // 发送给所有群成员
            var groupMembers = await _context.GroupMembers
                .Where(gm => gm.GroupId == groupId)
                .Select(gm => gm.UserId.ToString())
                .ToListAsync();

            var memberConnections = _connections
                .Where(c => groupMembers.Contains(c.Value))
                .Select(c => c.Key)
                .ToList();

            await Clients.Clients(memberConnections).SendAsync("ReceiveMessage", messageData);
        }

        // 加入群聊房间
        public async Task JoinGroup(int groupId)
        {
            var userId = int.Parse(Context.User!.FindFirst(ClaimTypes.NameIdentifier)!.Value);
            
            var membership = await _context.GroupMembers
                .FirstOrDefaultAsync(gm => gm.GroupId == groupId && gm.UserId == userId);
            
            if (membership != null)
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, $"Group_{groupId}");
            }
        }

        // 离开群聊房间
        public async Task LeaveGroup(int groupId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Group_{groupId}");
        }

        private async Task NotifyFriendsStatusChange(int userId, bool isOnline)
        {
            var friends = await _context.Friendships
                .Where(f => (f.RequesterId == userId || f.AddresseeId == userId) && 
                           f.Status == FriendshipStatus.Accepted)
                .Select(f => f.RequesterId == userId ? f.AddresseeId : f.RequesterId)
                .ToListAsync();

            var friendConnections = _connections
                .Where(c => friends.Contains(int.Parse(c.Value)))
                .Select(c => c.Key)
                .ToList();

            if (friendConnections.Any())
            {
                await Clients.Clients(friendConnections).SendAsync("FriendStatusChanged", new
                {
                    UserId = userId,
                    IsOnline = isOnline
                });
            }
        }
    }
}
