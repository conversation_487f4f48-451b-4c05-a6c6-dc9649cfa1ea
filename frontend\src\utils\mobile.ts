// 移动端适配工具函数

/**
 * 设置移动端视口高度
 * 解决移动端浏览器地址栏导致的视口高度问题
 */
export function setMobileViewport() {
  const setVH = () => {
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  }

  // 初始设置
  setVH()

  // 监听窗口大小变化
  window.addEventListener('resize', setVH)
  window.addEventListener('orientationchange', () => {
    setTimeout(setVH, 100) // 延迟执行，等待方向改变完成
  })
}

/**
 * 检测是否为移动设备
 */
export function isMobile(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

/**
 * 检测是否为iOS设备
 */
export function isIOS(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检测是否为Android设备
 */
export function isAndroid(): boolean {
  return /Android/.test(navigator.userAgent)
}

/**
 * 获取设备像素比
 */
export function getDevicePixelRatio(): number {
  return window.devicePixelRatio || 1
}

/**
 * 防止iOS Safari缩放
 */
export function preventIOSZoom() {
  if (isIOS()) {
    // 防止双击缩放
    let lastTouchEnd = 0
    document.addEventListener('touchend', (event) => {
      const now = new Date().getTime()
      if (now - lastTouchEnd <= 300) {
        event.preventDefault()
      }
      lastTouchEnd = now
    }, false)

    // 防止手势缩放
    document.addEventListener('gesturestart', (event) => {
      event.preventDefault()
    })
  }
}

/**
 * 处理键盘弹出
 */
export function handleKeyboard() {
  if (isMobile()) {
    const originalHeight = window.innerHeight

    window.addEventListener('resize', () => {
      const currentHeight = window.innerHeight
      const heightDiff = originalHeight - currentHeight

      // 如果高度差超过150px，认为是键盘弹出
      if (heightDiff > 150) {
        document.body.classList.add('keyboard-open')
      } else {
        document.body.classList.remove('keyboard-open')
      }
    })
  }
}

/**
 * 设置状态栏样式（PWA）
 */
export function setStatusBarStyle() {
  // 设置状态栏样式为深色内容
  const metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', '#ffffff')
  }

  // 设置苹果设备状态栏样式
  const metaAppleStatusBar = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]')
  if (metaAppleStatusBar) {
    metaAppleStatusBar.setAttribute('content', 'default')
  }
}

/**
 * 优化触摸滚动
 */
export function optimizeTouchScroll() {
  // 为滚动容器添加平滑滚动
  const scrollContainers = document.querySelectorAll('.message-list, .chat-list, .contacts-content')
  scrollContainers.forEach(container => {
    container.addEventListener('touchstart', () => {
      container.style.webkitOverflowScrolling = 'touch'
    })
  })
}

/**
 * 处理安全区域
 */
export function handleSafeArea() {
  // 检测是否支持安全区域
  if (CSS.supports('padding: env(safe-area-inset-top)')) {
    document.documentElement.classList.add('safe-area-supported')
  }

  // 为iPhone X系列设备添加特殊类名
  if (isIOS() && window.screen.height >= 812) {
    document.documentElement.classList.add('iphone-x')
  }
}

/**
 * 初始化移动端适配
 */
export function initMobileAdaptation() {
  setMobileViewport()
  preventIOSZoom()
  handleKeyboard()
  setStatusBarStyle()
  handleSafeArea()
  
  // DOM加载完成后执行
  document.addEventListener('DOMContentLoaded', () => {
    optimizeTouchScroll()
  })
}

/**
 * 获取屏幕尺寸信息
 */
export function getScreenInfo() {
  return {
    width: window.screen.width,
    height: window.screen.height,
    availWidth: window.screen.availWidth,
    availHeight: window.screen.availHeight,
    devicePixelRatio: getDevicePixelRatio(),
    orientation: window.screen.orientation?.type || 'unknown'
  }
}

/**
 * 检测网络状态
 */
export function getNetworkInfo() {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
  
  if (connection) {
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    }
  }
  
  return null
}

/**
 * 振动反馈（如果支持）
 */
export function vibrate(pattern: number | number[] = 100) {
  if ('vibrate' in navigator) {
    navigator.vibrate(pattern)
  }
}

/**
 * 复制到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      const result = document.execCommand('copy')
      document.body.removeChild(textArea)
      return result
    }
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}
